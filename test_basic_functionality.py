#!/usr/bin/env python3
"""
Basic functionality test for Guardrail Agent without OpenAI dependency
This script tests the core logic without requiring API keys
"""

import json
import sys
import os
from unittest.mock import MagicMock

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_process_compliance():
    """Test process compliance without OpenAI"""
    print("🔍 Testing Process Compliance...")
    
    from agents.process_compliance import ProcessCompliance
    
    compliance = ProcessCompliance()
    
    # Test with valid file validation output
    with open('test_data/file_validation_output.json') as f:
        test_data = json.load(f)
    
    with open('config/agent_profiles.yaml') as f:
        import yaml
        profiles = yaml.safe_load(f)
    
    profile = profiles['FileValidationAgent']
    issues = compliance.verify_process(test_data, profile)
    
    print(f"   ✅ File validation compliance check: {len(issues)} issues found")
    if issues:
        for issue in issues:
            print(f"      - {issue}")
    
    return len(issues) == 0

def test_security_monitor_rules():
    """Test security monitor rule-based scanning"""
    print("🔍 Testing Security Monitor (Rules Only)...")
    
    from agents.security_monitor import SecurityMonitor
    
    # Create security monitor without OpenAI client
    security = SecurityMonitor(client=None)
    
    # Test data with potential sensitive info
    test_output = {
        "data": "User SSN: ***********",
        "email": "<EMAIL>"
    }
    
    patterns = [
        r"\b\d{3}-\d{2}-\d{4}\b",  # SSN
        r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"  # Email
    ]
    
    findings = security.scan_output(test_output, patterns)
    
    print(f"   ✅ Security scan found {len(findings)} issues")
    for finding in findings:
        print(f"      - {finding}")
    
    return len(findings) > 0  # Should find issues

def test_configuration_loading():
    """Test configuration file loading"""
    print("🔍 Testing Configuration Loading...")
    
    try:
        # Mock OpenAI client to avoid API dependency
        mock_client = MagicMock()
        
        # Import after mocking
        from agents.guardrail_agent import GuardrailAgent
        
        # This should work without API key if we mock properly
        import openai
        original_openai = openai.OpenAI
        openai.OpenAI = MagicMock(return_value=mock_client)
        
        try:
            agent = GuardrailAgent("fake-api-key")
            print("   ✅ Configuration loaded successfully")
            print(f"   ✅ Found {len(agent.agent_profiles)} agent profiles")
            print(f"   ✅ Found {len(agent.guardrail_rules)} rule categories")
            return True
        finally:
            openai.OpenAI = original_openai
            
    except Exception as e:
        print(f"   ❌ Configuration loading failed: {e}")
        return False

def test_basic_audit_flow():
    """Test basic audit flow without AI calls"""
    print("🔍 Testing Basic Audit Flow...")
    
    try:
        # Mock all AI-dependent components
        mock_client = MagicMock()
        
        import openai
        original_openai = openai.OpenAI
        openai.OpenAI = MagicMock(return_value=mock_client)
        
        try:
            from agents.guardrail_agent import GuardrailAgent
            
            agent = GuardrailAgent("fake-api-key")
            
            # Mock the AI-dependent methods
            agent.auditor.audit_reasoning = MagicMock(return_value=[])
            agent.security._ai_based_scan = MagicMock(return_value=[])
            
            # Load test data
            with open('test_data/file_validation_output.json') as f:
                test_data = json.load(f)
            
            # Run audit
            report = agent.audit_agent_output(test_data)
            
            print(f"   ✅ Audit completed with status: {report['status']}")
            print(f"   ✅ Report contains: {list(report.keys())}")
            
            return report['status'] in ['PASSED', 'WARNING', 'CRITICAL']
            
        finally:
            openai.OpenAI = original_openai
            
    except Exception as e:
        print(f"   ❌ Basic audit flow failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all basic tests"""
    print("🚀 Running Basic Functionality Tests for Guardrail Agent\n")
    
    tests = [
        test_process_compliance,
        test_security_monitor_rules,
        test_configuration_loading,
        test_basic_audit_flow
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            print(f"   {'✅ PASSED' if result else '❌ FAILED'}\n")
        except Exception as e:
            print(f"   ❌ FAILED with exception: {e}\n")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic functionality tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
