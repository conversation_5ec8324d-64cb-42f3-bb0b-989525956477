{"metadata": {"agent_type": "SP_Parser_Agent", "agent_version": "1.5.2", "processing_time_ms": 7200, "task_id": "sp-parse-validation-rules"}, "rules_json": {"validation_rules": [{"rule_id": "NAV-01", "field": "nav_value", "constraint": "NOT NULL AND > 0", "error_message": "NAV value must be positive"}, {"rule_id": "FUND-ID-02", "field": "fund_id", "constraint": "LENGTH = 10 AND PATTERN = 'FUND[0-9]{6}'", "error_message": "Fund ID must follow FUND######ß format"}]}, "column_template": {"fund_id": "VARCHAR(10)", "nav_value": "DECIMAL(15,4)", "effective_date": "DATE"}, "parsing_confidence": 0.96}