import json
from datetime import datetime

class TelemetryLogger:
    def __init__(self, log_file="audit_logs.jsonl"):
        self.log_file = log_file
    
    def log_audit_start(self, metadata):
        """Log the start of an audit process"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event": "AUDIT_START",
            "agent_type": metadata.get("agent_type", "UNKNOWN"),
            "agent_version": metadata.get("agent_version", "UNKNOWN"),
            "task_id": metadata.get("task_id", "UNKNOWN")
        }
        self._write_log(log_entry)
    
    def log_audit_complete(self, report):
        """Log the completion of an audit"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event": "AUDIT_COMPLETE",
            "agent_type": report.get("agent_type", "UNKNOWN"),
            "status": report.get("status", "UNKNOWN"),
            "processing_time_ms": report.get("processing_time_ms", 0),
            "findings_count": {
                "process_issues": len(report.get("findings", {}).get("process_issues", [])),
                "reasoning_issues": len(report.get("findings", {}).get("reasoning_issues", [])),
                "security_issues": len(report.get("findings", {}).get("security_issues", []))
            }
        }
        self._write_log(log_entry)
    
    def log_escalation(self, escalation):
        """Log an escalation event"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event": "ESCALATION",
            "reason": escalation.get("reason", "Unknown"),
            "severity": escalation.get("severity", "MEDIUM"),
            "task_id": escalation.get("task_id", "UNKNOWN")
        }
        self._write_log(log_entry)
    
    def _write_log(self, log_entry):
        """Append log entry to file"""
        with open(self.log_file, "a") as f:
            f.write(json.dumps(log_entry) + "\n")