def get_reasoning_prompt(agent_type):
    """Get agent-specific reasoning audit prompt"""
    base_prompt = """
    You are an AI Reasoning Auditor. Your task is to audit the AGENT'S WORK, not the raw data.
    Verify the logical consistency and reasoning quality without redoing the agent's work.
    
    AGENT OUTPUT:
    {agent_output}
    
    EXPECTED OUTPUT STRUCTURE:
    {expected_structure}
    
    INSTRUCTIONS:
    """
    
    agent_specific = {
        "FileValidationAgent": """
        1. Check if error messages logically match the reported issues
        2. Verify invalid/valid counts match error messages
        3. Confirm schema violations align with actual schema
        4. Ensure no contradictions in validation report
        RESPONSE FORMAT: 'VALID' or 'INCONSISTENCIES: [list]'
        """,
        
        "DataValidationAgent": """
        1. Verify rule justifications match business rules
        2. Check overrides have proper explanations
        3. Confirm no rule was selectively ignored without reason
        4. Ensure temporal logic was consistently applied
        RESPONSE FORMAT: 'VALID' or 'ISSUES: [list]'
        """,
        
        "SP_Parser_Agent": """
        1. Check extracted rules match SP intent
        2. Verify no critical constraints were missed
        3. Confirm rule-to-SP mapping accuracy
        4. Ensure no hallucinated constraints
        RESPONSE FORMAT: 'VALID' or 'ISSUES: [list]'
        """
    }
    
    return base_prompt + agent_specific.get(agent_type, "")