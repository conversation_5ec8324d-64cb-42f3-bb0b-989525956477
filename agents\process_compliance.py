import json

class ProcessCompliance:
    def verify_process(self, agent_output, profile):
        """Verify agent followed its defined process"""
        issues = []

        # Validate input structure
        if 'metadata' not in agent_output:
            issues.append("Missing metadata in agent output")
            return issues

        metadata = agent_output['metadata']
        
        # 1. Check processing time
        max_time = profile.get('max_processing_time_ms', 5000)
        if 'processing_time_ms' in metadata:
            if metadata['processing_time_ms'] > max_time:
                issues.append(f"Processing time exceeded ({metadata['processing_time_ms']}ms > {max_time}ms)")
        else:
            issues.append("Missing processing_time_ms in metadata")
        
        # 2. Verify output structure
        if 'expected_output_structure' in profile:
            expected_keys = set(profile['expected_output_structure'])
            actual_keys = set(agent_output.keys())
            missing_keys = expected_keys - actual_keys

            if missing_keys:
                issues.append(f"Missing output elements: {', '.join(missing_keys)}")

            # Only flag extra keys as warnings, not errors (more flexible)
            extra_keys = actual_keys - expected_keys
            if extra_keys and len(extra_keys) > 2:  # Allow some flexibility
                issues.append(f"Many unexpected output elements: {', '.join(list(extra_keys)[:3])}...")
        
        # 3. Agent-specific process checks
        agent_type = metadata['agent_type']
        if agent_type == "FileValidationAgent":
            if 'validation_coverage' in agent_output:
                if agent_output['validation_coverage'] < profile['min_coverage']:
                    issues.append(f"Validation coverage insufficient: {agent_output['validation_coverage']*100}% < {profile['min_coverage']*100}%")
        
        elif agent_type == "DataValidationAgent":
            if 'override_rate' in agent_output:
                if agent_output['override_rate'] > profile['max_override_rate']:
                    issues.append(f"High override rate: {agent_output['override_rate']*100}% > {profile['max_override_rate']*100}%")

        elif agent_type == "SP_Parser_Agent":
            if 'parsing_confidence' in agent_output and 'min_rules_coverage' in profile:
                if agent_output['parsing_confidence'] < profile['min_rules_coverage']:
                    issues.append(f"Parsing confidence insufficient: {agent_output['parsing_confidence']*100}% < {profile['min_rules_coverage']*100}%")

        return issues