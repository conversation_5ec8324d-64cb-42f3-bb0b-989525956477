import json

class ProcessCompliance:
    def verify_process(self, agent_output, profile):
        """Verify agent followed its defined process"""
        issues = []
        metadata = agent_output['metadata']
        
        # 1. Check processing time
        max_time = profile.get('max_processing_time_ms', 5000)
        if metadata['processing_time_ms'] > max_time:
            issues.append(f"Processing time exceeded ({metadata['processing_time_ms']}ms > {max_time}ms)")
        
        # 2. Verify output structure
        expected_keys = set(profile['expected_output_structure'])
        actual_keys = set(agent_output.keys())
        missing_keys = expected_keys - actual_keys
        extra_keys = actual_keys - expected_keys
        
        if missing_keys:
            issues.append(f"Missing output elements: {', '.join(missing_keys)}")
        if extra_keys:
            issues.append(f"Unexpected output elements: {', '.join(extra_keys)}")
        
        # 3. Agent-specific process checks
        agent_type = metadata['agent_type']
        if agent_type == "FileValidationAgent":
            if 'validation_coverage' in agent_output:
                if agent_output['validation_coverage'] < profile['min_coverage']:
                    issues.append(f"Validation coverage insufficient: {agent_output['validation_coverage']*100}% < {profile['min_coverage']*100}%")
        
        elif agent_type == "DataValidationAgent":
            if 'override_rate' in agent_output:
                if agent_output['override_rate'] > profile['max_override_rate']:
                    issues.append(f"High override rate: {agent_output['override_rate']*100}% > {profile['max_override_rate']*100}%")
        
        return issues