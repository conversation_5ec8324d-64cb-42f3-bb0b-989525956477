scenarios:
  - name: Valid File Validation Output
    description: Audit clean output from FileValidationAgent
    test_data: file_validation_output.json
    expected_result: PASSED

  - name: Data Validation with Security Issue
    description: Audit output containing sensitive data
    test_data: data_validation_output.json
    modifications:
      - path: override_justifications
        value: ["Client SSN *********** approved for override"]
    expected_result: CRITICAL

  - name: SP Parser with Process Violation
    description: Audit parser that missed key constraints
    test_data: sp_parser_output.json
    modifications:
      - path: parsing_confidence
        value: 0.82
    expected_result: WARNING