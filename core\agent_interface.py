class AgentInterface:
    @staticmethod
    def validate_output_structure(output, expected_structure):
        """Validate agent output against expected structure"""
        errors = []
        
        # Check for required top-level keys
        for key in expected_structure:
            if key not in output:
                errors.append(f"Missing required field: {key}")
        
        # Check metadata structure
        if "metadata" in output:
            meta_errors = AgentInterface.validate_metadata(output["metadata"])
            errors.extend(meta_errors)
        
        return errors
    
    @staticmethod
    def validate_metadata(metadata):
        """Validate metadata structure"""
        errors = []
        required_fields = ["agent_type", "agent_version", "processing_time_ms", "task_id", "timestamp"]
        
        for field in required_fields:
            if field not in metadata:
                errors.append(f"Metadata missing required field: {field}")
        
        return errors
    
    @staticmethod
    def create_metadata(agent_type, version, processing_time, task_id):
        """Create standardized metadata object"""
        return {
            "agent_type": agent_type,
            "agent_version": version,
            "processing_time_ms": processing_time,
            "task_id": task_id,
            "timestamp": datetime.utcnow().isoformat()
        }