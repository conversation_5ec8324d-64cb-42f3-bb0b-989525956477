import re
import json
from core.prompt_templates import get_security_prompt
import openai

class SecurityMonitor:
    def __init__(self, api_key):
        openai.api_key = api_key
    
    def scan_output(self, agent_output, sensitive_patterns):
        """Hybrid approach to detect sensitive data"""
        # Convert output to text for scanning
        text_rep = json.dumps(agent_output)
        
        # Rule-based scanning
        rule_findings = self._rule_based_scan(text_rep, sensitive_patterns)
        
        # AI-based scanning
        ai_findings = self._ai_based_scan(text_rep)
        
        return rule_findings + ai_findings
    
    def _rule_based_scan(self, text, patterns):
        """Scan using regex patterns"""
        findings = []
        for pattern in patterns:
            if re.search(pattern, text):
                # Mask the pattern for security
                masked_pattern = pattern[:5] + "..." + pattern[-5:] if len(pattern) > 10 else pattern
                findings.append(f"Sensitive pattern detected: {masked_pattern}")
        return findings
    
    def _ai_based_scan(self, text):
        """Use AI for contextual sensitive data detection"""
        prompt = get_security_prompt() + f"\n\nAGENT OUTPUT:\n{text[:5000]}"  # Limit token usage
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-4-turbo",
                messages=[{"role": "system", "content": "You are a security auditor"}],
                temperature=0.1,
                max_tokens=300
            )
            result = response.choices[0].message['content'].strip()
            
            # Parse AI response
            if "CLEAN" in result:
                return []
            if "ISSUES:" in result:
                issues = result.split("ISSUES:")[1].split("\n")
                return [issue.strip() for issue in issues if issue.strip()]
            return [result]
        except Exception as e:
            return [f"Security scan failed: {str(e)}"]