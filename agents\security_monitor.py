import re
import json
from core.prompt_templates import get_security_prompt

class SecurityMonitor:
    def __init__(self, client=None):
        self.client = client
    
    def scan_output(self, agent_output, sensitive_patterns):
        """Hybrid approach to detect sensitive data"""
        try:
            # Convert output to text for scanning
            text_rep = json.dumps(agent_output)

            # Rule-based scanning
            rule_findings = self._rule_based_scan(text_rep, sensitive_patterns or [])

            # AI-based scanning (only if client is available)
            ai_findings = []
            if hasattr(self, 'client') and self.client:
                ai_findings = self._ai_based_scan(text_rep)

            return rule_findings + ai_findings
        except Exception as e:
            return [f"Security scan failed: {str(e)}"]
    
    def _rule_based_scan(self, text, patterns):
        """Scan using regex patterns"""
        findings = []
        for pattern in patterns:
            try:
                matches = re.findall(pattern, text)
                if matches:
                    # Mask the actual matches for security
                    for match in matches:
                        if len(str(match)) > 6:
                            masked_match = str(match)[:2] + "***" + str(match)[-2:]
                        else:
                            masked_match = "***"
                        findings.append(f"Sensitive pattern detected: {masked_match}")
            except re.error as e:
                findings.append(f"Invalid regex pattern: {str(e)}")
        return findings
    
    def _ai_based_scan(self, text):
        """Use AI for contextual sensitive data detection"""
        prompt = get_security_prompt() + f"\n\nAGENT OUTPUT:\n{text[:5000]}"  # Limit token usage

        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",  # Using GPT-4 mini as requested
                messages=[
                    {"role": "system", "content": "You are a security auditor"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=300
            )
            result = response.choices[0].message.content.strip()

            # Parse AI response
            if "CLEAN" in result:
                return []
            if "ISSUES:" in result:
                issues = result.split("ISSUES:")[1].split("\n")
                return [issue.strip() for issue in issues if issue.strip()]
            return [result]
        except Exception as e:
            return [f"Security scan failed: {str(e)}"]