import openai
import yaml
from .reasoning_auditor import <PERSON>ing<PERSON>udi<PERSON>
from .process_compliance import <PERSON><PERSON>omp<PERSON>
from .security_monitor import SecurityMonitor
from core.telemetry_logger import Telemetry<PERSON>ogger
from core.escalation_handler import EscalationHandler

class GuardrailAgent:
    def __init__(self, api_key):
        openai.api_key = api_key
        self.logger = TelemetryLogger()
        self.auditor = ReasoningAuditor(api_key)
        self.compliance = ProcessCompliance()
        self.security = SecurityMonitor()
        self.escalation = EscalationHandler()
        
        # Load configuration
        with open('config/agent_profiles.yaml') as f:
            self.agent_profiles = yaml.safe_load(f)
        with open('config/guardrail_rules.yaml') as f:
            self.guardrail_rules = yaml.safe_load(f)

    def audit_agent_output(self, agent_output):
        """Audit agent output without duplicating its work"""
        agent_type = agent_output['metadata']['agent_type']
        self.logger.log_audit_start(agent_output['metadata'])
        
        # 1. Process compliance check
        process_issues = self.compliance.verify_process(
            agent_output, 
            self.agent_profiles[agent_type]
        )
        
        # 2. Reasoning integrity audit
        reasoning_issues = self.auditor.audit_reasoning(
            agent_output,
            self.agent_profiles[agent_type]['expected_output_structure']
        )
        
        # 3. Security and leakage check
        security_issues = self.security.scan_output(
            agent_output,
            self.guardrail_rules['sensitive_patterns']
        )
        
        # Compile findings
        findings = {
            'process_issues': process_issues,
            'reasoning_issues': reasoning_issues,
            'security_issues': security_issues
        }
        
        # 4. Generate audit report
        report = self._generate_audit_report(findings, agent_type)
        
        # 5. Escalation decision
        if self._requires_escalation(findings, agent_type):
            escalation = self.escalation.generate_escalation(
                findings,
                agent_output['metadata']
            )
            report['escalation'] = escalation
        
        self.logger.log_audit_complete(report)
        return report

    def _generate_audit_report(self, findings, agent_type):
        """Compile audit findings into structured report"""
        report = {
            'agent_type': agent_type,
            'audit_timestamp': datetime.utcnow().isoformat(),
            'findings': findings,
            'status': 'PASSED'
        }
        
        # Set status based on findings severity
        if findings['security_issues'] or findings['reasoning_issues']:
            report['status'] = 'CRITICAL'
        elif findings['process_issues']:
            report['status'] = 'WARNING'
        
        return report

    def _requires_escalation(self, findings, agent_type):
        """Determine if human escalation is needed"""
        critical_agents = ['DataValidationAgent', 'SP_Parser_Agent']
        if findings['security_issues']:
            return True
        if findings['reasoning_issues'] and agent_type in critical_agents:
            return True
        return False