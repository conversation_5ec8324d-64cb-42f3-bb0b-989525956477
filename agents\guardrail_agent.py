import openai
import yaml
from datetime import datetime
from .reasoning_auditor import <PERSON>ing<PERSON>udi<PERSON>
from .process_compliance import ProcessCompliance
from .security_monitor import SecurityMonitor
from core.telemetry_logger import TelemetryLogger
from core.escalation_handler import EscalationHandler

class GuardrailAgent:
    def __init__(self, api_key):
        self.client = openai.OpenAI(api_key=api_key)
        self.logger = TelemetryLogger()
        self.auditor = ReasoningAuditor(self.client)
        self.compliance = ProcessCompliance()
        self.security = SecurityMonitor(self.client)
        self.escalation = EscalationHandler()
        
        # Load configuration with error handling
        try:
            with open('config/agent_profiles.yaml', 'r', encoding='utf-8') as f:
                self.agent_profiles = yaml.safe_load(f)
            with open('config/guardrail_rules.yaml', 'r', encoding='utf-8') as f:
                self.guardrail_rules = yaml.safe_load(f)

            # Validate configuration structure
            if not isinstance(self.agent_profiles, dict):
                raise ValueError("agent_profiles.yaml must contain a dictionary")
            if not isinstance(self.guardrail_rules, dict):
                raise ValueError("guardrail_rules.yaml must contain a dictionary")

        except FileNotFoundError as e:
            raise FileNotFoundError(f"Configuration file not found: {e}")
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML configuration: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration: {e}")

    def audit_agent_output(self, agent_output):
        """Audit agent output without duplicating its work"""
        try:
            # Validate input structure
            if not isinstance(agent_output, dict) or 'metadata' not in agent_output:
                raise ValueError("Invalid agent output: missing metadata")

            agent_type = agent_output['metadata']['agent_type']
            if agent_type not in self.agent_profiles:
                raise ValueError(f"Unknown agent type: {agent_type}")

            self.logger.log_audit_start(agent_output['metadata'])

            # 1. Process compliance check
            process_issues = self.compliance.verify_process(
                agent_output,
                self.agent_profiles[agent_type]
            )

            # 2. Reasoning integrity audit
            reasoning_issues = self.auditor.audit_reasoning(
                agent_output,
                self.agent_profiles[agent_type]['expected_output_structure']
            )

            # 3. Security and leakage check
            security_issues = self.security.scan_output(
                agent_output,
                self.guardrail_rules['sensitive_patterns']
            )

            # Compile findings
            findings = {
                'process_issues': process_issues,
                'reasoning_issues': reasoning_issues,
                'security_issues': security_issues
            }

            # 4. Generate audit report
            report = self._generate_audit_report(findings, agent_type)

            # 5. Escalation decision
            if self._requires_escalation(findings, agent_type):
                escalation = self.escalation.generate_escalation(
                    findings,
                    agent_output['metadata']
                )
                report['escalation'] = escalation

            self.logger.log_audit_complete(report)
            return report

        except Exception as e:
            error_report = {
                'agent_type': agent_output.get('metadata', {}).get('agent_type', 'UNKNOWN'),
                'audit_timestamp': datetime.utcnow().isoformat(),
                'status': 'ERROR',
                'error': str(e),
                'findings': {
                    'process_issues': [],
                    'reasoning_issues': [],
                    'security_issues': []
                }
            }
            self.logger.log_audit_complete(error_report)
            return error_report

    def _generate_audit_report(self, findings, agent_type):
        """Compile audit findings into structured report"""
        report = {
            'agent_type': agent_type,
            'audit_timestamp': datetime.utcnow().isoformat(),
            'findings': findings,
            'status': 'PASSED'
        }
        
        # Set status based on findings severity
        if findings['security_issues'] or findings['reasoning_issues']:
            report['status'] = 'CRITICAL'
        elif findings['process_issues']:
            report['status'] = 'WARNING'
        
        return report

    def _requires_escalation(self, findings, agent_type):
        """Determine if human escalation is needed"""
        critical_agents = ['DataValidationAgent', 'SP_Parser_Agent']
        if findings['security_issues']:
            return True
        if findings['reasoning_issues'] and agent_type in critical_agents:
            return True
        return False