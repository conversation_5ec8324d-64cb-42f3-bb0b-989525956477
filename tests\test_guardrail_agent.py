import unittest
import json
from unittest.mock import patch, MagicMock
from agents.guardrail_agent import GuardrailAgent

class TestGuardrailAgent(unittest.TestCase):
    @patch('openai.OpenAI')
    def setUp(self, mock_openai):
        # Mock the OpenAI client
        mock_client = MagicMock()
        mock_openai.return_value = mock_client
        self.agent = GuardrailAgent("test-api-key")
        with open('test_data/file_validation_output.json') as f:
            self.file_val_data = json.load(f)
        with open('test_data/data_validation_output.json') as f:
            self.data_val_data = json.load(f)
        with open('test_data/sp_parser_output.json') as f:
            self.parser_data = json.load(f)
    
    @patch('agents.reasoning_auditor.ReasoningAuditor.audit_reasoning')
    @patch('agents.process_compliance.ProcessCompliance.verify_process')
    @patch('agents.security_monitor.SecurityMonitor.scan_output')
    def test_clean_audit(self, mock_security, mock_process, mock_reasoning):
        # Mock all checks to return clean
        mock_security.return_value = []
        mock_process.return_value = []
        mock_reasoning.return_value = []
        
        report = self.agent.audit_agent_output(self.file_val_data)
        self.assertEqual(report['status'], 'PASSED')
        self.assertNotIn('escalation', report)
    
    @patch('agents.security_monitor.SecurityMonitor.scan_output')
    def test_security_escalation(self, mock_security):
        mock_security.return_value = ["SSN pattern detected"]
        
        report = self.agent.audit_agent_output(self.data_val_data)
        self.assertEqual(report['status'], 'CRITICAL')
        self.assertIn('escalation', report)
        self.assertEqual(report['escalation']['type'], 'SECURITY')
    
    @patch('agents.process_compliance.ProcessCompliance.verify_process')
    def test_process_violation(self, mock_process):
        mock_process.return_value = ["Processing time exceeded"]
        
        report = self.agent.audit_agent_output(self.parser_data)
        self.assertEqual(report['status'], 'WARNING')
        self.assertNotIn('escalation', report)
    
    @patch('agents.reasoning_auditor.ReasoningAuditor.audit_reasoning')
    def test_reasoning_issue(self, mock_reasoning):
        mock_reasoning.return_value = ["Logical inconsistency found"]
        
        report = self.agent.audit_agent_output(self.data_val_data)
        self.assertEqual(report['status'], 'CRITICAL')
        self.assertIn('escalation', report)
        self.assertEqual(report['escalation']['type'], 'REASONING')
    
    @patch('core.telemetry_logger.TelemetryLogger.log_audit_start')
    @patch('core.telemetry_logger.TelemetryLogger.log_audit_complete')
    def test_telemetry_logging(self, mock_complete, mock_start):
        self.agent.audit_agent_output(self.file_val_data)
        self.assertTrue(mock_start.called)
        self.assertTrue(mock_complete.called)
    
    def test_agent_identification(self):
        report = self.agent.audit_agent_output(self.file_val_data)
        self.assertEqual(report['agent_type'], 'FileValidationAgent')
    
    @patch('agents.security_monitor.SecurityMonitor.scan_output')
    @patch('agents.reasoning_auditor.ReasoningAuditor.audit_reasoning')
    def test_critical_priority(self, mock_reasoning, mock_security):
        # Security issues should take priority
        mock_security.return_value = ["SSN detected"]
        mock_reasoning.return_value = ["Logic issue"]
        
        report = self.agent.audit_agent_output(self.data_val_data)
        self.assertEqual(report['escalation']['type'], 'SECURITY')

if __name__ == '__main__':
    unittest.main()